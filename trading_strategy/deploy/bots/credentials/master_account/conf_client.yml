####################################
###   client_config_map config   ###
####################################

instance_id: da1a30622aec40d9f1a9f1333c2b845f12ec456d

# Fetch trading pairs from all exchanges if True, otherwise fetch only from connected exchanges.
fetch_pairs_from_all_exchanges: false

log_level: INFO

debug_console: false

strategy_report_interval: 900.0

logger_override_whitelist:
- hummingbot.strategy.arbitrage
- hummingbot.strategy.cross_exchange_market_making
- conf


kill_switch_mode: {}

# What to auto-fill in the prompt after each import command (start/config)
autofill_import: disabled

# MQTT Bridge configuration.
mqtt_bridge:
  mqtt_host: localhost
  mqtt_port: 1883
  mqtt_username: ''
  mqtt_password: ''
  mqtt_namespace: hbot
  mqtt_ssl: false
  mqtt_logger: true
  mqtt_notifier: true
  mqtt_commands: true
  mqtt_events: true
  mqtt_autostart: true

# Error log sharing
send_error_logs: true

# Can store the previous strategy ran for quick retrieval.
previous_strategy: some-strategy.yml

# Advanced database options, currently supports SQLAlchemy's included dialects
# Reference: https://docs.sqlalchemy.org/en/13/dialects/
# To use an instance of SQLite DB the required configuration is 
#   db_engine: sqlite
# To use a DBMS the required configuration is
#   db_host: 127.0.0.1
#   db_port: 3306
#   db_username: username
#   db_password: password
#   db_name: dbname
db_mode:
  db_engine: sqlite

# Balance Limit Configurations
# e.g. Setting USDT and BTC limits on Binance.
# balance_asset_limit:
#   binance:
#     BTC: 0.1
#     USDT: 1000
balance_asset_limit:
  kucoin: {}
  ndax_testnet: {}
  huobi: {}
  bitmart: {}
  polkadex: {}
  hitbtc: {}
  ndax: {}
  foxbit: {}
  bitmex_testnet: {}
  coinbase_pro: {}
  bybit: {}
  binance_paper_trade: {}
  bitfinex: {}
  kucoin_paper_trade: {}
  okx: {}
  binance_us: {}
  injective_v2: {}
  ascend_ex: {}
  binance: {}
  bybit_testnet: {}
  kraken: {}
  mexc: {}
  vertex: {}
  gate_io_paper_trade: {}
  gate_io: {}
  woo_x: {}
  woo_x_testnet: {}
  btc_markets: {}
  vertex_testnet: {}
  mock_paper_exchange: {}
  bitmex: {}
  ascend_ex_paper_trade: {}

# Fixed gas price (in Gwei) for Ethereum transactions
manual_gas_price: 50.0

# Gateway API Configurations
# default host to only use localhost
# Port need to match the final installation port for Gateway
gateway:
  gateway_api_host: localhost
  gateway_api_port: '15888'

certs_path: /Users/<USER>/Documents/work/hummingbot/certs

# Whether to enable aggregated order and trade data collection
anonymized_metrics_mode:
  anonymized_metrics_interval_min: 15.0

# Command Shortcuts
# Define abbreviations for often used commands
# or batch grouped commands together
command_shortcuts:
- command: spreads
  help: Set bid and ask spread
  arguments:
  - Bid Spread
  - Ask Spread
  output:
  - config bid_spread $1
  - config ask_spread $2

# A source for rate oracle, currently ascend_ex, binance, coin_gecko, coin_cap, kucoin, gate_io
rate_oracle_source:
  name: binance

# A universal token which to display tokens values in, e.g. USD,EUR,BTC
global_token:
  global_token_name: USDT
  global_token_symbol: $

# Percentage of API rate limits (on any exchange and any end point) allocated to this bot instance.
# Enter 50 to indicate 50%. E.g. if the API rate limit is 100 calls per second, and you allocate 
# 50% to this setting, the bot will have a maximum (limit) of 50 calls per second
rate_limits_share_pct: 100.0

commands_timeout:
  create_command_timeout: 10.0
  other_commands_timeout: 30.0

# Tabulate table format style (https://github.com/astanin/python-tabulate#table-format)
tables_format: psql

paper_trade:
  paper_trade_exchanges:
  - binance
  - kucoin
  - ascend_ex
  - gate_io
  paper_trade_account_balance:
    BTC: 1.0
    USDT: 1000.0
    ONE: 1000.0
    USDQ: 1000.0
    TUSD: 1000.0
    ETH: 10.0
    WETH: 10.0
    USDC: 1000.0
    DAI: 1000.0

color:
  top_pane: '#000000'
  bottom_pane: '#000000'
  output_pane: '#262626'
  input_pane: '#1C1C1C'
  logs_pane: '#121212'
  terminal_primary: '#5FFFD7'
  primary_label: '#5FFFD7'
  secondary_label: '#FFFFFF'
  success_label: '#5FFFD7'
  warning_label: '#FFFF00'
  info_label: '#5FD7FF'
  error_label: '#FF0000'
  gold_label: '#FFD700'
  silver_label: '#C0C0C0'
  bronze_label: '#CD7F32'

# The tick size is the frequency with which the clock notifies the time iterators by calling the
# c_tick() method, that means for example that if the tick size is 1, the logic of the strategy 
# will run every second.
tick_size: 1.0

market_data_collection:
  market_data_collection_enabled: false
  market_data_collection_interval: 60
  market_data_collection_depth: 20

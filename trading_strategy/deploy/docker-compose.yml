services:
  dashboard:
    container_name: dashboard
    image: hummingbot/dashboard:latest
    ports:
      - "8501:8501"
    environment:
        - AUTH_SYSTEM_ENABLED=False
        - BACKEND_API_HOST=backend-api
        - BACKEND_API_PORT=8000
        - BACKEND_API_USERNAME=admin
        - BACKEND_API_PASSWORD=admin
    volumes:
      - ./credentials.yml:/home/<USER>/credentials.yml
      - ./pages:/home/<USER>/frontend/pages
    networks:
        - emqx-bridge
  backend-api:
    container_name: backend-api
    image: hummingbot/backend-api:latest
    ports:
      - "8000:8000"
    volumes:
      - ./bots:/backend-api/bots
      - /var/run/docker.sock:/var/run/docker.sock
    env_file:
      - .env
    environment:
      - BROKER_HOST=emqx
      - BROKER_PORT=1883
      - USERNAME=admin
      - PASSWORD=admin
    networks:
      - emqx-bridge
  emqx:
    container_name: hummingbot-broker
    image: emqx:5
    restart: unless-stopped
    environment:
      - EMQX_NAME=emqx
      - EMQX_HOST=node1.emqx.local
      - EMQX_CLUSTER__DISCOVERY_STRATEGY=static
      - EMQX_CLUSTER__STATIC__SEEDS=[<EMAIL>]
      - EMQX_LOADED_PLUGINS="emqx_recon,emqx_retainer,emqx_management,emqx_dashboard"
    volumes:
      - emqx-data:/opt/emqx/data
      - emqx-log:/opt/emqx/log
      - emqx-etc:/opt/emqx/etc
    ports:
      - "1883:1883"  # mqtt:tcp
      - "8883:8883"  # mqtt:tcp:ssl
      - "8083:8083"  # mqtt:ws
      - "8084:8084"  # mqtt:ws:ssl
      - "8081:8081"  # http:management
      - "18083:18083"  # http:dashboard
      - "61613:61613"  # web-stomp gateway
    networks:
      emqx-bridge:
        aliases:
          - node1.emqx.local
    healthcheck:
      test: [ "CMD", "/opt/emqx/bin/emqx_ctl", "status" ]
      interval: 5s
      timeout: 25s
      retries: 5

networks:
  emqx-bridge:
    driver: bridge

volumes:
  emqx-data: { }
  emqx-log: { }
  emqx-etc: { }

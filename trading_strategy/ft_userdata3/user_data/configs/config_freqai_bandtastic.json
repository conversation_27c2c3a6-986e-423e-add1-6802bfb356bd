{"max_open_trades": 5, "stake_currency": "USDT", "stake_amount": "unlimited", "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "timeframe": "15m", "dry_run": true, "dry_run_wallet": 10000, "cancel_open_orders_on_exit": false, "trading_mode": "futures", "margin_mode": "isolated", "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "", "secret": "", "ccxt_config": {}, "ccxt_async_config": {}, "pair_whitelist": ["BTC/USDT:USDT", "ETH/USDT:USDT", "SOL/USDT:USDT", "PEPE/USDT:USDT", "DOGE/USDT:USDT", "XRP/USDT:USDT", "ADA/USDT:USDT", "LINK/USDT:USDT", "MATIC/USDT:USDT", "AVAX/USDT:USDT"], "pair_blacklist": [".*_PREMIUM", ".*_DOWN", ".*_UP"]}, "pairlists": [{"method": "StaticPairList"}], "edge": {"enabled": false, "process_throttle_secs": 3600, "calculate_since_number_of_days": 7, "allowed_risk": 0.01, "stoploss_range_min": -0.01, "stoploss_range_max": -0.1, "stoploss_range_step": -0.01, "minimum_winrate": 0.6, "minimum_expectancy": 0.2, "min_trade_number": 10, "max_trade_duration_minute": 1440, "remove_pumps": false}, "freqai": {"enabled": true, "purge_old_models": 2, "train_period_days": 30, "backtest_period_days": 7, "live_retrain_hours": 0, "expiration_hours": 1, "identifier": "BandtasticFreqAI_v1", "feature_parameters": {"include_timeframes": ["5m", "15m", "1h", "4h"], "include_corr_pairlist": ["BTC/USDT:USDT", "ETH/USDT:USDT"], "label_period_candles": 24, "include_shifted_candles": 2, "DI_threshold": 0.9, "weight_factor": 0.9, "principal_component_analysis": false, "use_SVM_to_remove_outliers": true, "svm_params": {"shuffle": false, "nu": 0.01}, "use_DBSCAN_to_remove_outliers": false, "indicator_periods_candles": [10, 20, 50]}, "data_split_parameters": {"test_size": 0.33, "shuffle": false, "random_state": 1}, "model_training_parameters": {"n_estimators": 800, "learning_rate": 0.02, "max_depth": 9, "min_child_weight": 1, "subsample": 0.9, "colsample_bytree": 0.9, "random_state": 1}}, "bot_name": "BandtasticFreqAI", "force_entry_enable": false, "initial_state": "running", "internals": {"process_throttle_secs": 5}}
from datetime import datetime
import talib.abstract as ta
import numpy as np
import pandas as pd
from functools import reduce
from pandas import DataFrame
import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    IStrategy,
    CategoricalParameter,
    DecimalParameter,
    IntParameter
)

class Bandtastic_Combined(IStrategy):
    INTERFACE_VERSION = 3
    timeframe = '15m'
    can_short: bool = True

    # 多空共享的风险管理参数
    minimal_roi = {
        "0": 0.232,
        "60": 0.37,
        "120": 0.51,
        "240": 0.212
    }

    stoploss = -0.25
    trailing_stop = True
    trailing_stop_positive = 0.02
    trailing_stop_positive_offset = 0.25
    trailing_only_offset_is_reached = True

    process_only_new_candles = True
    startup_candle_count = 999
    use_exit_signal = True
    exit_profit_only = True
    ignore_roi_if_entry_signal = False

    # 定义热门币列表（只做多）
    top_coins = [
        "BTC/USDT:USDT", "ETH/USDT:USDT", "SOL/USDT:USDT",
        "FARTCOIN/USDT","PEPE/USDT:USDT","DOGE/USDT:USDT",
        "XRP/USDT:USDT","WIF/USDT:USDT", "SUI/USDT:USDT",
        "ADA/USDT:USDT", "LTC/USDT:USDT","LINK/USDT:USDT",
        "BCH/USDT:USDT","ONDO/USDT:USDT","TRX/USDT:USDT",
        "VIRTUAL/USDT:USDT"
    ]

    # ==================== 做多参数 ====================
    # 做多入场参数
    long_fastema = IntParameter(low=1, high=236, default=211, space='buy', optimize=True, load=True)
    long_slowema = IntParameter(low=1, high=250, default=250, space='buy', optimize=True, load=True)
    long_rsi = IntParameter(low=25, high=70, default=31, space='buy', optimize=True, load=True)
    long_mfi = IntParameter(low=25, high=70, default=29, space='buy', optimize=True, load=True)
    
    long_rsi_enabled = CategoricalParameter([True, False], space='buy', optimize=True, default=False)
    long_mfi_enabled = CategoricalParameter([True, False], space='buy', optimize=True, default=False)
    long_ema_enabled = CategoricalParameter([True, False], space='buy', optimize=True, default=False)
    long_trigger = CategoricalParameter(
        ["bb_lower1", "bb_lower2", "bb_lower3", "bb_lower4"], 
        default="bb_lower1", 
        space="buy"
    )

    # 做多出场参数
    exit_long_fastema = IntParameter(low=1, high=365, default=5, space='sell', optimize=True, load=True)
    exit_long_slowema = IntParameter(low=1, high=365, default=20, space='sell', optimize=True, load=True)
    exit_long_rsi = IntParameter(low=50, high=100, default=79, space='sell', optimize=True, load=True)
    exit_long_mfi = IntParameter(low=50, high=100, default=77, space='sell', optimize=True, load=True)
    
    exit_long_rsi_enabled = CategoricalParameter([True, False], space='sell', optimize=True, default=False)
    exit_long_mfi_enabled = CategoricalParameter([True, False], space='sell', optimize=True, default=True)
    exit_long_ema_enabled = CategoricalParameter([True, False], space='sell', optimize=True, default=False)
    exit_long_trigger = CategoricalParameter(
        ["sell-bb_upper1", "sell-bb_upper2", "sell-bb_upper3", "sell-bb_upper4"], 
        default="sell-bb_upper2", 
        space="sell"
    )

    # ==================== 做空参数 ====================
    # 做空入场参数
    short_fastema = IntParameter(low=1, high=366, default=211, space='sell', optimize=True, load=True)
    short_slowema = IntParameter(low=1, high=365, default=250, space='sell', optimize=True, load=True)
    short_rsi = IntParameter(low=30, high=100, default=63, space='sell', optimize=True, load=True)
    short_mfi = IntParameter(low=30, high=100, default=57, space='sell', optimize=True, load=True)
    
    short_rsi_enabled = CategoricalParameter([True, False], default=True, space='sell', optimize=True)
    short_mfi_enabled = CategoricalParameter([True, False], default=True, space='sell', optimize=True)
    short_ema_enabled = CategoricalParameter([True, False], default=True, space='sell', optimize=True)
    short_trigger = CategoricalParameter(
        ["sell-bb_upper1", "sell-bb_upper2", "sell-bb_upper3", "sell-bb_upper4"], 
        default="sell-bb_upper2", 
        space="sell"
    )

    # 做空出场参数
    exit_short_fastema = IntParameter(low=1, high=236, default=7, space='buy', optimize=True, load=True)
    exit_short_slowema = IntParameter(low=1, high=250, default=6, space='buy', optimize=True, load=True)
    exit_short_rsi = IntParameter(low=30, high=50, default=37, space='buy', optimize=True, load=True)
    exit_short_mfi = IntParameter(low=30, high=50, default=31, space='buy', optimize=True, load=True)
    
    exit_short_rsi_enabled = CategoricalParameter([True, False], space='buy', optimize=True, default=True)
    exit_short_mfi_enabled = CategoricalParameter([True, False], space='buy', optimize=True, default=True)
    exit_short_ema_enabled = CategoricalParameter([True, False], space='buy', optimize=True, default=True)
    exit_short_trigger = CategoricalParameter(
        ["bb_lower1", "bb_lower2", "bb_lower3", "bb_lower4"], 
        default="bb_lower1", 
        space="buy"
    )

    # 杠杆参数
    long_leverage = IntParameter(1, 3, default=3, space="buy", optimize=False, load=False)
    short_leverage = IntParameter(1, 3, default=5, space="sell", optimize=False, load=False)

    def _calculate_ema(self, dataframe: DataFrame, period: int) -> None:
        """动态计算EMA指标（如果需要）"""
        col_name = f'EMA_{period}'
        if col_name not in dataframe:
            dataframe[col_name] = ta.EMA(dataframe, timeperiod=period)

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # RSI 和 MFI
        dataframe['rsi'] = ta.RSI(dataframe)
        dataframe['mfi'] = ta.MFI(dataframe)
    
        # Bollinger Bands 1,2,3 and 4
        bollinger1 = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), window=20, stds=1)
        dataframe['bb_lowerband1'] = bollinger1['lower']
        dataframe['bb_middleband1'] = bollinger1['mid']
        dataframe['bb_upperband1'] = bollinger1['upper']

        bollinger2 = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), window=20, stds=2)
        dataframe['bb_lowerband2'] = bollinger2['lower']
        dataframe['bb_middleband2'] = bollinger2['mid']
        dataframe['bb_upperband2'] = bollinger2['upper']

        bollinger3 = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), window=20, stds=3)
        dataframe['bb_lowerband3'] = bollinger3['lower']
        dataframe['bb_middleband3'] = bollinger3['mid']
        dataframe['bb_upperband3'] = bollinger3['upper']

        bollinger4 = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), window=20, stds=4)
        dataframe['bb_lowerband4'] = bollinger4['lower']
        dataframe['bb_middleband4'] = bollinger4['mid']
        dataframe['bb_upperband4'] = bollinger4['upper']

        for period in set(
                list(self.exit_long_fastema.range)
                + list(self.exit_long_slowema.range)
                + list(self.exit_short_fastema.range)
                + list(self.exit_short_slowema.range)
            ):
            dataframe[f'EMA_{period}'] = ta.EMA(dataframe, timeperiod=period)

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        pair = metadata['pair']
        is_top_coin = pair in self.top_coins
        # 初始化信号列
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        
        # ==================== 做多入场条件 ====================
        long_conditions = []
        
        # 只对热门币执行做多逻辑
        if is_top_coin:
            # 指标条件
            if self.long_ema_enabled.value:
                fast_period = self.long_fastema.value
                slow_period = self.long_slowema.value
                
                # 动态计算需要的EMA
                self._calculate_ema(dataframe, fast_period)
                self._calculate_ema(dataframe, slow_period)
                
                long_conditions.append(
                    dataframe[f'EMA_{fast_period}'] > 
                    dataframe[f'EMA_{slow_period}']
                )
        
            # 指标条件
            if self.long_rsi_enabled.value:
                long_conditions.append(dataframe['rsi'] < self.long_rsi.value)
            if self.long_mfi_enabled.value:
                long_conditions.append(dataframe['mfi'] < self.long_mfi.value)
        
            # 触发条件
            trigger = self.long_trigger.value
            if 'bb_lower1' in trigger:
                long_conditions.append(dataframe["close"] < dataframe['bb_lowerband1'])
            elif 'bb_lower2' in trigger:
                long_conditions.append(dataframe["close"] < dataframe['bb_lowerband2'])
            elif 'bb_lower3' in trigger:
                long_conditions.append(dataframe["close"] < dataframe['bb_lowerband3'])
            elif 'bb_lower4' in trigger:
                long_conditions.append(dataframe["close"] < dataframe['bb_lowerband4'])
            
            # 基础条件
            long_conditions.append(dataframe['volume'] > 0)
        
            # 组合条件
            if long_conditions:
                dataframe.loc[reduce(lambda x, y: x & y, long_conditions), 'enter_long'] = 1

        # ==================== 做空入场条件 ====================
        short_conditions = []
        
        if not is_top_coin:
            # 指标条件
            if self.short_ema_enabled.value:
                fast_period = self.short_fastema.value
                slow_period = self.short_slowema.value
                
                # 动态计算需要的EMA
                self._calculate_ema(dataframe, fast_period)
                self._calculate_ema(dataframe, slow_period)
                
                short_conditions.append(
                    dataframe[f'EMA_{fast_period}'] < 
                    dataframe[f'EMA_{slow_period}']
                )

            if self.short_rsi_enabled.value:
                short_conditions.append(dataframe['rsi'] > self.short_rsi.value)
            if self.short_mfi_enabled.value:
                short_conditions.append(dataframe['mfi'] > self.short_mfi.value)
                
            # 触发条件
            trigger = self.short_trigger.value
            if 'bb_upper1' in trigger:
                short_conditions.append(dataframe["close"] > dataframe['bb_upperband1'])
            elif 'bb_upper2' in trigger:
                short_conditions.append(dataframe["close"] > dataframe['bb_upperband2'])
            elif 'bb_upper3' in trigger:
                short_conditions.append(dataframe["close"] > dataframe['bb_upperband3'])
            elif 'bb_upper4' in trigger:
                short_conditions.append(dataframe["close"] > dataframe['bb_upperband4'])
            
            # 基础条件
            short_conditions.append(dataframe['volume'] > 0)
            
            # 组合条件
            if short_conditions:
                dataframe.loc[reduce(lambda x, y: x & y, short_conditions), 'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        pair = metadata['pair']
        is_top_coin = pair in self.top_coins
        # 初始化信号列
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0
        
        # ==================== 做多出场条件 ====================
        exit_long_conditions = []
        
        # 只对热门币执行做多出场逻辑
        if is_top_coin:
            # 指标条件
            if self.exit_long_ema_enabled.value:
                fast_period = self.exit_long_fastema.value
                slow_period = self.exit_long_slowema.value
                
                # 动态计算需要的EMA
                self._calculate_ema(dataframe, fast_period)
                self._calculate_ema(dataframe, slow_period)
                
                exit_long_conditions.append(
                    dataframe[f'EMA_{fast_period}'] < 
                    dataframe[f'EMA_{slow_period}']
                )
            
            if self.exit_long_rsi_enabled.value:
                exit_long_conditions.append(dataframe['rsi'] > self.exit_long_rsi.value)
                
            if self.exit_long_mfi_enabled.value:
                exit_long_conditions.append(dataframe['mfi'] > self.exit_long_mfi.value)

            # 触发条件
            trigger = self.exit_long_trigger.value
            if 'bb_upper1' in trigger:
                exit_long_conditions.append(dataframe["close"] > dataframe['bb_upperband1'])
            elif 'bb_upper2' in trigger:
                exit_long_conditions.append(dataframe["close"] > dataframe['bb_upperband2'])
            elif 'bb_upper3' in trigger:
                exit_long_conditions.append(dataframe["close"] > dataframe['bb_upperband3'])
            elif 'bb_upper4' in trigger:
                exit_long_conditions.append(dataframe["close"] > dataframe['bb_upperband4'])
            
            # 基础条件
            exit_long_conditions.append(dataframe['volume'] > 0)
            
            # 组合条件
            if exit_long_conditions:
                dataframe.loc[reduce(lambda x, y: x & y, exit_long_conditions), 'exit_long'] = 1

        # ==================== 做空出场条件 ====================
        exit_short_conditions = []
        
        # 只对非热门币执行做空出场逻辑
        if not is_top_coin:
            # 指标条件
            if self.exit_short_ema_enabled.value:
                fast_period = self.exit_short_fastema.value
                slow_period = self.exit_short_slowema.value
                
                # 动态计算需要的EMA
                self._calculate_ema(dataframe, fast_period)
                self._calculate_ema(dataframe, slow_period)
                
                exit_short_conditions.append(
                    dataframe[f'EMA_{fast_period}'] > 
                    dataframe[f'EMA_{slow_period}']
                )
            
            if self.exit_short_rsi_enabled.value:
                exit_short_conditions.append(dataframe['rsi'] < self.exit_short_rsi.value)
                
            if self.exit_short_mfi_enabled.value:
                exit_short_conditions.append(dataframe['mfi'] < self.exit_short_mfi.value)
                
            # 触发条件
            trigger = self.exit_short_trigger.value
            if 'bb_lower1' in trigger:
                exit_short_conditions.append(dataframe["close"] < dataframe['bb_lowerband1'])
            elif 'bb_lower2' in trigger:
                exit_short_conditions.append(dataframe["close"] < dataframe['bb_lowerband2'])
            elif 'bb_lower3' in trigger:
                exit_short_conditions.append(dataframe["close"] < dataframe['bb_lowerband3'])
            elif 'bb_lower4' in trigger:
                exit_short_conditions.append(dataframe["close"] < dataframe['bb_lowerband4'])
            
            # 基础条件
            exit_short_conditions.append(dataframe['volume'] > 0)
            
            # 组合条件
            if exit_short_conditions:
                dataframe.loc[reduce(lambda x, y: x & y, exit_short_conditions), 'exit_short'] = 1

        return dataframe

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                proposed_leverage: float, max_leverage: float, side: str,
                **kwargs) -> float:
        """动态杠杆管理"""
        if side == 'long':
            return self.long_leverage.value
        elif side == 'short':
            return self.short_leverage.value
        return 1.0  # 默认杠杆
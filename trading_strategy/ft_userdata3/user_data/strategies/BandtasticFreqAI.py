from datetime import datetime
import talib.abstract as ta
import numpy as np
import pandas as pd
from functools import reduce
from pandas import DataFrame
import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    IStrategy,
    CategoricalParameter,
    DecimalParameter,
    IntParameter
)

class BandtasticFreqAI(IStrategy):
    """
    FreqAI-enhanced Bandtastic strategy that uses machine learning to predict BTC direction
    and integrates ML predictions with traditional technical analysis.
    
    Key Features:
    - ML-based direction prediction for BTC (up/down classification)
    - Maintains original hot coins (long only) vs other coins (short allowed) logic
    - Combines FreqAI predictions with Bollinger Bands, RSI, MFI, and EMA signals
    - Dynamic confidence-based entry filtering
    """
    
    INTERFACE_VERSION = 3
    timeframe = '15m'
    can_short: bool = True

    # Risk management parameters (same as original)
    minimal_roi = {
        "0": 0.232,
        "60": 0.37,
        "120": 0.51,
        "240": 0.212
    }

    stoploss = -0.25
    trailing_stop = True
    trailing_stop_positive = 0.02
    trailing_stop_positive_offset = 0.25
    trailing_only_offset_is_reached = True

    process_only_new_candles = True
    startup_candle_count = 999
    use_exit_signal = True
    exit_profit_only = True
    ignore_roi_if_entry_signal = False

    # Hot coins list (only long positions)
    top_coins = [
        "BTC/USDT:USDT", "ETH/USDT:USDT", "SOL/USDT:USDT",
        "FARTCOIN/USDT","PEPE/USDT:USDT","DOGE/USDT:USDT",
        "XRP/USDT:USDT","WIF/USDT:USDT", "SUI/USDT:USDT",
        "ADA/USDT:USDT", "LTC/USDT:USDT","LINK/USDT:USDT",
        "BCH/USDT:USDT","ONDO/USDT:USDT","TRX/USDT:USDT",
        "VIRTUAL/USDT:USDT"
    ]

    # FreqAI confidence thresholds
    ml_confidence_long = DecimalParameter(0.5, 0.9, default=0.65, space='buy', optimize=True)
    ml_confidence_short = DecimalParameter(0.5, 0.9, default=0.65, space='sell', optimize=True)
    
    # Traditional strategy parameters (simplified from original)
    long_rsi_threshold = IntParameter(25, 70, default=31, space='buy', optimize=True)
    long_mfi_threshold = IntParameter(25, 70, default=29, space='buy', optimize=True)
    short_rsi_threshold = IntParameter(30, 100, default=63, space='sell', optimize=True)
    short_mfi_threshold = IntParameter(30, 100, default=57, space='sell', optimize=True)
    
    # Bollinger Bands trigger selection
    long_bb_trigger = CategoricalParameter(
        ["bb_lower1", "bb_lower2", "bb_lower3", "bb_lower4"], 
        default="bb_lower2", space="buy"
    )
    short_bb_trigger = CategoricalParameter(
        ["bb_upper1", "bb_upper2", "bb_upper3", "bb_upper4"], 
        default="bb_upper2", space="sell"
    )

    # Leverage parameters
    long_leverage = IntParameter(1, 3, default=3, space="buy", optimize=False)
    short_leverage = IntParameter(1, 5, default=5, space="sell", optimize=False)

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Populate indicators including FreqAI predictions and traditional technical indicators
        """
        # FreqAI integration - this must be called first
        dataframe = self.freqai.start(dataframe, metadata, self)
        
        # Traditional technical indicators
        dataframe['rsi'] = ta.RSI(dataframe)
        dataframe['mfi'] = ta.MFI(dataframe)
    
        # Bollinger Bands with multiple standard deviations
        for std in [1, 2, 3, 4]:
            bollinger = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), window=20, stds=std)
            dataframe[f'bb_lowerband{std}'] = bollinger['lower']
            dataframe[f'bb_middleband{std}'] = bollinger['mid']
            dataframe[f'bb_upperband{std}'] = bollinger['upper']

        # EMA indicators for trend confirmation
        for period in [5, 10, 20, 50, 200]:
            dataframe[f'EMA_{period}'] = ta.EMA(dataframe, timeperiod=period)

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Populate entry signals combining FreqAI predictions with traditional analysis
        """
        pair = metadata['pair']
        is_top_coin = pair in self.top_coins
        
        # Initialize signal columns
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        
        # Check if FreqAI predictions are available
        if '&s-direction' not in dataframe.columns or 'do_predict' not in dataframe.columns:
            self.logger.warning(f"Missing FreqAI predictions for {metadata['pair']} - using traditional signals only")
            return self._populate_traditional_entry_signals(dataframe, is_top_coin)
        
        # ==================== LONG ENTRY CONDITIONS ====================
        if is_top_coin:
            long_conditions = [
                dataframe['volume'] > 0,
                dataframe['do_predict'] == 1,  # FreqAI confidence check
                dataframe['&s-direction'] == 'up',  # ML predicts upward movement
                dataframe['&s-direction_confidence'] > self.ml_confidence_long.value,  # High confidence
            ]
            
            # Traditional technical conditions
            long_conditions.extend([
                dataframe['rsi'] < self.long_rsi_threshold.value,
                dataframe['mfi'] < self.long_mfi_threshold.value,
            ])
            
            # Bollinger Bands trigger
            bb_trigger = self.long_bb_trigger.value
            if bb_trigger == "bb_lower1":
                long_conditions.append(dataframe["close"] < dataframe['bb_lowerband1'])
            elif bb_trigger == "bb_lower2":
                long_conditions.append(dataframe["close"] < dataframe['bb_lowerband2'])
            elif bb_trigger == "bb_lower3":
                long_conditions.append(dataframe["close"] < dataframe['bb_lowerband3'])
            elif bb_trigger == "bb_lower4":
                long_conditions.append(dataframe["close"] < dataframe['bb_lowerband4'])
            
            # Apply long conditions
            if long_conditions:
                dataframe.loc[reduce(lambda x, y: x & y, long_conditions), 'enter_long'] = 1

        # ==================== SHORT ENTRY CONDITIONS ====================
        if not is_top_coin:
            short_conditions = [
                dataframe['volume'] > 0,
                dataframe['do_predict'] == 1,  # FreqAI confidence check
                dataframe['&s-direction'] == 'down',  # ML predicts downward movement
                dataframe['&s-direction_confidence'] > self.ml_confidence_short.value,  # High confidence
            ]
            
            # Traditional technical conditions
            short_conditions.extend([
                dataframe['rsi'] > self.short_rsi_threshold.value,
                dataframe['mfi'] > self.short_mfi_threshold.value,
            ])
            
            # Bollinger Bands trigger
            bb_trigger = self.short_bb_trigger.value
            if bb_trigger == "bb_upper1":
                short_conditions.append(dataframe["close"] > dataframe['bb_upperband1'])
            elif bb_trigger == "bb_upper2":
                short_conditions.append(dataframe["close"] > dataframe['bb_upperband2'])
            elif bb_trigger == "bb_upper3":
                short_conditions.append(dataframe["close"] > dataframe['bb_upperband3'])
            elif bb_trigger == "bb_upper4":
                short_conditions.append(dataframe["close"] > dataframe['bb_upperband4'])
            
            # Apply short conditions
            if short_conditions:
                dataframe.loc[reduce(lambda x, y: x & y, short_conditions), 'enter_short'] = 1

        return dataframe

    def _populate_traditional_entry_signals(self, dataframe: DataFrame, is_top_coin: bool) -> DataFrame:
        """
        Fallback to traditional signals when FreqAI predictions are not available
        """
        # This is a simplified version of the original strategy logic
        if is_top_coin:
            long_conditions = [
                dataframe['volume'] > 0,
                dataframe['rsi'] < self.long_rsi_threshold.value,
                dataframe['mfi'] < self.long_mfi_threshold.value,
                dataframe["close"] < dataframe['bb_lowerband2']
            ]
            if long_conditions:
                dataframe.loc[reduce(lambda x, y: x & y, long_conditions), 'enter_long'] = 1
        else:
            short_conditions = [
                dataframe['volume'] > 0,
                dataframe['rsi'] > self.short_rsi_threshold.value,
                dataframe['mfi'] > self.short_mfi_threshold.value,
                dataframe["close"] > dataframe['bb_upperband2']
            ]
            if short_conditions:
                dataframe.loc[reduce(lambda x, y: x & y, short_conditions), 'enter_short'] = 1
        
        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Populate exit signals based on FreqAI predictions and traditional analysis
        """
        pair = metadata['pair']
        is_top_coin = pair in self.top_coins

        # Initialize exit signal columns
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0

        # Check if FreqAI predictions are available
        if '&s-direction' not in dataframe.columns:
            return dataframe

        # Exit long positions when ML predicts downward movement
        if is_top_coin:
            exit_long_conditions = [
                dataframe['volume'] > 0,
                dataframe['do_predict'] == 1,
                (dataframe['&s-direction'] == 'down') |
                (dataframe['&s-direction_confidence'] < 0.4),  # Low confidence exit
                dataframe['rsi'] > 70,  # Overbought condition
            ]

            if exit_long_conditions:
                dataframe.loc[reduce(lambda x, y: x & y, exit_long_conditions), 'exit_long'] = 1

        # Exit short positions when ML predicts upward movement
        if not is_top_coin:
            exit_short_conditions = [
                dataframe['volume'] > 0,
                dataframe['do_predict'] == 1,
                (dataframe['&s-direction'] == 'up') |
                (dataframe['&s-direction_confidence'] < 0.4),  # Low confidence exit
                dataframe['rsi'] < 30,  # Oversold condition
            ]

            if exit_short_conditions:
                dataframe.loc[reduce(lambda x, y: x & y, exit_short_conditions), 'exit_short'] = 1

        return dataframe

    def set_freqai_targets(self, dataframe: DataFrame, metadata: dict, **kwargs) -> DataFrame:
        """
        Set FreqAI targets for direction prediction (classification)
        """
        # Set class names for binary classification
        self.freqai.class_names = ["down", "up"]

        # Prediction horizon (number of candles to look ahead)
        prediction_horizon = self.freqai_info["feature_parameters"].get("label_period_candles", 24)

        # Calculate future price direction
        future_price = dataframe["close"].shift(-prediction_horizon)
        current_price = dataframe["close"]

        # Binary classification: up if future price > current price, down otherwise
        dataframe["&s-direction"] = np.where(
            future_price > current_price, "up", "down"
        )

        # Additional target: confidence score based on magnitude of price change
        price_change_pct = (future_price / current_price - 1) * 100
        dataframe["&s-direction_confidence"] = np.abs(price_change_pct) / 10.0  # Normalize to 0-1 range
        dataframe["&s-direction_confidence"] = np.clip(dataframe["&s-direction_confidence"], 0, 1)

        return dataframe

    def feature_engineering_expand_all(self, dataframe: DataFrame, period: int, metadata: dict, **kwargs) -> DataFrame:
        """
        Feature engineering with automatic expansion across periods
        All features must be prepended with % to be recognized by FreqAI
        """
        # Technical indicators that will be expanded across different periods
        dataframe[f"%-rsi-{period}"] = ta.RSI(dataframe, timeperiod=period)
        dataframe[f"%-mfi-{period}"] = ta.MFI(dataframe, timeperiod=period)
        dataframe[f"%-adx-{period}"] = ta.ADX(dataframe, timeperiod=period)
        dataframe[f"%-cci-{period}"] = ta.CCI(dataframe, timeperiod=period)
        dataframe[f"%-roc-{period}"] = ta.ROC(dataframe, timeperiod=period)

        # Moving averages
        dataframe[f"%-sma-{period}"] = ta.SMA(dataframe, timeperiod=period)
        dataframe[f"%-ema-{period}"] = ta.EMA(dataframe, timeperiod=period)
        dataframe[f"%-tema-{period}"] = ta.TEMA(dataframe, timeperiod=period)

        # Bollinger Bands features
        bb = qtpylib.bollinger_bands(dataframe["close"], window=period, stds=2)
        dataframe[f"%-bb_upper-{period}"] = bb["upper"]
        dataframe[f"%-bb_middle-{period}"] = bb["mid"]
        dataframe[f"%-bb_lower-{period}"] = bb["lower"]
        dataframe[f"%-bb_width-{period}"] = (bb["upper"] - bb["lower"]) / bb["mid"]
        dataframe[f"%-bb_position-{period}"] = (dataframe["close"] - bb["lower"]) / (bb["upper"] - bb["lower"])

        # MACD features
        macd = ta.MACD(dataframe, fastperiod=int(period*0.5), slowperiod=period, signalperiod=int(period*0.3))
        dataframe[f"%-macd-{period}"] = macd["macd"]
        dataframe[f"%-macd_signal-{period}"] = macd["macdsignal"]
        dataframe[f"%-macd_hist-{period}"] = macd["macdhist"]

        return dataframe

    def feature_engineering_expand_basic(self, dataframe: DataFrame, metadata: dict, **kwargs) -> DataFrame:
        """
        Basic feature engineering that will be expanded across timeframes and pairs
        """
        # Price-based features
        dataframe["%-pct_change"] = dataframe["close"].pct_change()
        dataframe["%-price_position"] = dataframe["close"] / dataframe["high"]
        dataframe["%-hl_ratio"] = (dataframe["high"] - dataframe["low"]) / dataframe["close"]

        # Volume features
        dataframe["%-volume"] = dataframe["volume"]
        dataframe["%-volume_sma"] = dataframe["volume"].rolling(20).mean()
        dataframe["%-volume_ratio"] = dataframe["volume"] / dataframe["%-volume_sma"]

        # Volatility features
        dataframe["%-volatility"] = dataframe["close"].rolling(20).std()
        dataframe["%-atr"] = ta.ATR(dataframe, timeperiod=14)

        # Price momentum
        dataframe["%-momentum_1"] = dataframe["close"] / dataframe["close"].shift(1) - 1
        dataframe["%-momentum_3"] = dataframe["close"] / dataframe["close"].shift(3) - 1
        dataframe["%-momentum_5"] = dataframe["close"] / dataframe["close"].shift(5) - 1

        return dataframe

    def feature_engineering_standard(self, dataframe: DataFrame, metadata: dict, **kwargs) -> DataFrame:
        """
        Standard feature engineering for time-based and market structure features
        """
        # Time-based features
        dataframe["%-day_of_week"] = (dataframe["date"].dt.dayofweek + 1) / 7
        dataframe["%-hour_of_day"] = (dataframe["date"].dt.hour + 1) / 24
        dataframe["%-month_of_year"] = (dataframe["date"].dt.month) / 12

        # Market structure features
        dataframe["%-is_top_coin"] = 1 if metadata["pair"] in self.top_coins else 0

        # Price level features
        dataframe["%-price_level"] = np.log(dataframe["close"])
        dataframe["%-price_normalized"] = (dataframe["close"] - dataframe["close"].rolling(100).mean()) / dataframe["close"].rolling(100).std()

        return dataframe

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                proposed_leverage: float, max_leverage: float, side: str,
                **kwargs) -> float:
        """
        Dynamic leverage management based on ML confidence and position side
        """
        if side == 'long':
            return self.long_leverage.value
        elif side == 'short':
            return self.short_leverage.value
        return 1.0

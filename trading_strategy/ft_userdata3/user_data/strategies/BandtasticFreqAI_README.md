# BandtasticFreqAI Strategy

## Overview

BandtasticFreqAI is an advanced trading strategy that combines machine learning predictions with traditional technical analysis. It uses FreqAI to predict BTC price direction (up/down) and integrates these predictions with the proven Bandtastic strategy logic.

## Key Features

### 🤖 Machine Learning Integration
- **Binary Classification**: Predicts whether BTC price will go up or down
- **Confidence Scoring**: Uses prediction confidence to filter entry signals
- **Multi-timeframe Analysis**: Incorporates 5m, 15m, 1h, and 4h timeframes
- **Feature Engineering**: 50+ technical indicators and market features

### 📊 Traditional Technical Analysis
- **Bollinger Bands**: Multiple standard deviation levels (1σ, 2σ, 3σ, 4σ)
- **RSI & MFI**: Momentum and money flow indicators
- **EMA Trends**: Multiple exponential moving averages
- **Volume Analysis**: Volume-based confirmation signals

### 🎯 Smart Position Management
- **Hot Coins Strategy**: Long-only positions for top cryptocurrencies
- **Altcoin Strategy**: Both long and short positions for other coins
- **Dynamic Leverage**: Adjustable leverage based on position type
- **ML-based Exits**: Exit signals based on prediction confidence

## Strategy Logic

### Entry Conditions

#### Long Positions (Hot Coins Only)
```python
Conditions:
1. FreqAI predicts "up" direction
2. Prediction confidence > threshold (default 0.65)
3. RSI < 31 (oversold)
4. MFI < 29 (money flow oversold)
5. Price touches Bollinger Band lower level
6. Volume > 0
```

#### Short Positions (Non-Hot Coins)
```python
Conditions:
1. FreqAI predicts "down" direction
2. Prediction confidence > threshold (default 0.65)
3. RSI > 63 (overbought)
4. MFI > 57 (money flow overbought)
5. Price touches Bollinger Band upper level
6. Volume > 0
```

### Exit Conditions

#### Long Exit
- FreqAI predicts "down" direction
- Prediction confidence drops below 0.4
- RSI > 70 (overbought)

#### Short Exit
- FreqAI predicts "up" direction
- Prediction confidence drops below 0.4
- RSI < 30 (oversold)

## Hot Coins List

The strategy treats these coins as "hot coins" (long-only):
- BTC/USDT:USDT
- ETH/USDT:USDT
- SOL/USDT:USDT
- PEPE/USDT:USDT
- DOGE/USDT:USDT
- XRP/USDT:USDT
- WIF/USDT:USDT
- SUI/USDT:USDT
- ADA/USDT:USDT
- LTC/USDT:USDT
- LINK/USDT:USDT
- BCH/USDT:USDT
- ONDO/USDT:USDT
- TRX/USDT:USDT
- VIRTUAL/USDT:USDT

## Configuration

### FreqAI Settings
```json
"freqai": {
    "enabled": true,
    "train_period_days": 30,
    "label_period_candles": 24,
    "include_timeframes": ["5m", "15m", "1h", "4h"],
    "include_corr_pairlist": ["BTC/USDT:USDT", "ETH/USDT:USDT"],
    "indicator_periods_candles": [10, 20, 50]
}
```

### Risk Management
- **Stop Loss**: -25%
- **Trailing Stop**: Enabled with 2% positive offset
- **ROI**: Progressive ROI from 23.2% to 51%
- **Leverage**: 3x for longs, 5x for shorts

## Usage Instructions

### 1. Setup FreqAI Environment
```bash
# Install FreqAI dependencies
pip install freqtrade[freqai]

# For advanced ML models
pip install catboost lightgbm xgboost
```

### 2. Download Historical Data
```bash
freqtrade download-data --exchange binance --pairs BTC/USDT:USDT ETH/USDT:USDT --timeframes 5m 15m 1h 4h --days 60
```

### 3. Train the Model
```bash
freqtrade freqai-train --config config_freqai_bandtastic.json --strategy BandtasticFreqAI
```

### 4. Backtest
```bash
freqtrade backtesting --config config_freqai_bandtastic.json --strategy BandtasticFreqAI --timerange 20231201-20240301
```

### 5. Live Trading
```bash
freqtrade trade --config config_freqai_bandtastic.json --strategy BandtasticFreqAI
```

## Feature Engineering

### Technical Indicators (Auto-expanded)
- RSI, MFI, ADX, CCI, ROC
- SMA, EMA, TEMA
- Bollinger Bands (width, position)
- MACD (line, signal, histogram)

### Price Features
- Price change percentages
- Price position within high-low range
- Volatility measures (ATR, rolling std)
- Momentum indicators (1, 3, 5 period)

### Market Structure
- Time-based features (day, hour, month)
- Volume ratios and patterns
- Price normalization
- Hot coin classification

## Optimization Parameters

### ML Confidence Thresholds
- `ml_confidence_long`: 0.5-0.9 (default 0.65)
- `ml_confidence_short`: 0.5-0.9 (default 0.65)

### Technical Thresholds
- `long_rsi_threshold`: 25-70 (default 31)
- `long_mfi_threshold`: 25-70 (default 29)
- `short_rsi_threshold`: 30-100 (default 63)
- `short_mfi_threshold`: 30-100 (default 57)

### Bollinger Band Triggers
- Long: bb_lower1, bb_lower2, bb_lower3, bb_lower4
- Short: bb_upper1, bb_upper2, bb_upper3, bb_upper4

## Performance Tips

1. **Training Data**: Use at least 30 days of training data
2. **Retraining**: Retrain model weekly for best performance
3. **Pair Selection**: Focus on high-volume, liquid pairs
4. **Market Conditions**: Monitor model performance in different market regimes
5. **Confidence Tuning**: Adjust confidence thresholds based on market volatility

## Troubleshooting

### Common Issues
1. **Missing Predictions**: Check FreqAI model training status
2. **Low Performance**: Increase training data or adjust confidence thresholds
3. **No Trades**: Verify pair whitelist and market conditions
4. **Memory Issues**: Reduce feature complexity or training period

### Monitoring
- Check FreqAI logs for model training status
- Monitor prediction confidence distributions
- Track strategy performance vs pure ML signals
- Validate feature importance regularly

## Disclaimer

This strategy is for educational purposes. Always test thoroughly in paper trading before using real funds. Past performance does not guarantee future results.

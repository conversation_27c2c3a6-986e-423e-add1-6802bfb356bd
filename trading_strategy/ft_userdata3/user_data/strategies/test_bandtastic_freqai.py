#!/usr/bin/env python3
"""
Test script for BandtasticFreqAI strategy
Validates strategy structure and basic functionality
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the strategy path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from BandtasticFreqAI import BandtasticFreqAI
    print("✅ Strategy import successful")
except ImportError as e:
    print(f"❌ Strategy import failed: {e}")
    sys.exit(1)

def create_test_dataframe(length=1000):
    """Create a test dataframe with OHLCV data"""
    dates = pd.date_range(start='2023-01-01', periods=length, freq='15T')
    
    # Generate realistic price data
    np.random.seed(42)
    base_price = 50000
    price_changes = np.random.normal(0, 0.02, length)
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 1000))  # Minimum price floor
    
    # Create OHLCV data
    data = []
    for i, price in enumerate(prices):
        high = price * (1 + abs(np.random.normal(0, 0.01)))
        low = price * (1 - abs(np.random.normal(0, 0.01)))
        open_price = prices[i-1] if i > 0 else price
        close_price = price
        volume = np.random.uniform(1000000, 10000000)
        
        data.append({
            'date': dates[i],
            'open': open_price,
            'high': high,
            'low': low,
            'close': close_price,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('date', inplace=True)
    return df

def test_strategy_methods():
    """Test all strategy methods"""
    print("\n🧪 Testing strategy methods...")
    
    # Initialize strategy
    strategy = BandtasticFreqAI()
    
    # Mock FreqAI info
    strategy.freqai_info = {
        "feature_parameters": {
            "label_period_candles": 24,
            "include_timeframes": ["5m", "15m", "1h"],
            "indicator_periods_candles": [10, 20, 50]
        }
    }
    
    # Mock FreqAI object
    class MockFreqAI:
        def __init__(self):
            self.class_names = []
        
        def start(self, dataframe, metadata, strategy):
            # Add mock FreqAI predictions
            dataframe['&s-direction'] = np.random.choice(['up', 'down'], len(dataframe))
            dataframe['&s-direction_confidence'] = np.random.uniform(0.3, 0.9, len(dataframe))
            dataframe['do_predict'] = np.random.choice([0, 1], len(dataframe), p=[0.1, 0.9])
            return dataframe
    
    strategy.freqai = MockFreqAI()
    
    # Create test data
    df = create_test_dataframe(500)
    metadata = {'pair': 'BTC/USDT:USDT'}
    
    # Test feature engineering methods
    try:
        # Test expand_all
        df_test = df.copy()
        result = strategy.feature_engineering_expand_all(df_test, 20, metadata)
        assert len(result.columns) > len(df.columns), "feature_engineering_expand_all should add columns"
        print("✅ feature_engineering_expand_all works")
        
        # Test expand_basic
        df_test = df.copy()
        result = strategy.feature_engineering_expand_basic(df_test, metadata)
        assert len(result.columns) > len(df.columns), "feature_engineering_expand_basic should add columns"
        print("✅ feature_engineering_expand_basic works")
        
        # Test standard
        df_test = df.copy()
        result = strategy.feature_engineering_standard(df_test, metadata)
        assert len(result.columns) > len(df.columns), "feature_engineering_standard should add columns"
        print("✅ feature_engineering_standard works")
        
        # Test set_freqai_targets
        df_test = df.copy()
        result = strategy.set_freqai_targets(df_test, metadata)
        assert '&s-direction' in result.columns, "set_freqai_targets should add target columns"
        assert '&s-direction_confidence' in result.columns, "set_freqai_targets should add confidence column"
        print("✅ set_freqai_targets works")
        
    except Exception as e:
        print(f"❌ Feature engineering test failed: {e}")
        return False
    
    # Test indicator population
    try:
        df_test = df.copy()
        result = strategy.populate_indicators(df_test, metadata)
        required_indicators = ['rsi', 'mfi', 'bb_lowerband1', 'bb_upperband1', 'EMA_5']
        for indicator in required_indicators:
            assert indicator in result.columns, f"Missing indicator: {indicator}"
        print("✅ populate_indicators works")
    except Exception as e:
        print(f"❌ populate_indicators test failed: {e}")
        return False
    
    # Test entry trend
    try:
        df_test = result.copy()  # Use dataframe with indicators
        entry_result = strategy.populate_entry_trend(df_test, metadata)
        assert 'enter_long' in entry_result.columns, "Missing enter_long column"
        assert 'enter_short' in entry_result.columns, "Missing enter_short column"
        print("✅ populate_entry_trend works")
    except Exception as e:
        print(f"❌ populate_entry_trend test failed: {e}")
        return False
    
    # Test exit trend
    try:
        df_test = entry_result.copy()
        exit_result = strategy.populate_exit_trend(df_test, metadata)
        assert 'exit_long' in exit_result.columns, "Missing exit_long column"
        assert 'exit_short' in exit_result.columns, "Missing exit_short column"
        print("✅ populate_exit_trend works")
    except Exception as e:
        print(f"❌ populate_exit_trend test failed: {e}")
        return False
    
    # Test leverage function
    try:
        leverage = strategy.leverage('BTC/USDT:USDT', datetime.now(), 50000, 1, 10, 'long')
        assert isinstance(leverage, (int, float)), "Leverage should return a number"
        print("✅ leverage function works")
    except Exception as e:
        print(f"❌ leverage test failed: {e}")
        return False
    
    return True

def test_hot_coins_logic():
    """Test hot coins vs regular coins logic"""
    print("\n🎯 Testing hot coins logic...")
    
    strategy = BandtasticFreqAI()
    
    # Test hot coin
    assert 'BTC/USDT:USDT' in strategy.top_coins, "BTC should be in top coins"
    
    # Test non-hot coin
    test_pair = 'RANDOM/USDT:USDT'
    assert test_pair not in strategy.top_coins, "Random pair should not be in top coins"
    
    print("✅ Hot coins logic works")
    return True

def main():
    """Run all tests"""
    print("🚀 Starting BandtasticFreqAI Strategy Tests")
    print("=" * 50)
    
    tests = [
        test_strategy_methods,
        test_hot_coins_logic,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Strategy is ready for use.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the strategy code.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

from datetime import datetime
import talib.abstract as ta
import numpy as np  # noqa
import pandas as pd
from functools import reduce
from pandas import DataFrame
import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    IStrategy,
    CategoricalParameter,
    DecimalParameter,  # Import DecimalParameter for optimizable floats
    IntParameter
)

class Bandtastic_Short(IStrategy):
    INTERFACE_VERSION = 3
    timeframe = '15m'
    can_short: bool = True

    minimal_roi = {
        "0": 0.232,
        "60": 0.37,
        "120": 0.51,
        "240": 0.212
    }

    exit_loss_profit = {
        "profit_threshold": 0.27,  # 盈利15%后激活回调止损
        "loss_percentage": 0.02    # 允许从最高点回撤5%
    }

    stoploss = -0.75

    # stoploss_opt = DecimalParameter(-0.27, -0.02, default=-0.251, decimals=3, space='stoploss', optimize=True, load=True)

    startup_candle_count = 999

    use_exit_signal = True
    exit_profit_only = True
    ignore_roi_if_entry_signal = False

    # Trailing Stoploss
    trailing_stop = True
    # Percentage of profit needed to activate TSL
    trailing_stop_positive = 0.02
    # Distance from the peak for the TSL trigger (corrected and made optimizable)
    trailing_stop_positive_offset = 0.25
    # If true, TSL activates only after profit exceeds offset, not just positive value. Usually False.
    trailing_only_offset_is_reached = True

    process_only_new_candles = True
    # sell_fastema = IntParameter(low=1, high=365, default=7, space='sell', optimize=True, load=True)
    # sell_slowema = IntParameter(low=1, high=365, default=6, space='sell', optimize=True, load=True)
    # sell_rsi = IntParameter(low=30, high=100, default=57, space='sell', optimize=True, load=True)
    # sell_mfi = IntParameter(low=30, high=100, default=46, space='sell', optimize=True, load=True)

    # Hyperopt Sell Parameters (反向，用作做空时开仓)
    sell_fastema = IntParameter(low=1, high=366, default=211, space='sell', optimize=True, load=True)
    sell_slowema = IntParameter(low=1, high=365, default=250, space='sell', optimize=True, load=True)
    sell_rsi = IntParameter(low=30, high=100, default=61, space='sell', optimize=True, load=True)
    sell_mfi = IntParameter(low=30, high=100, default=55, space='sell', optimize=True, load=True)

    sell_rsi_enabled = CategoricalParameter([True, False], default=True, space='sell', optimize=True)
    sell_mfi_enabled = CategoricalParameter([True, False], default=True, space='sell', optimize=True)
    sell_ema_enabled = CategoricalParameter([True, False], default=True, space='sell', optimize=True)
    sell_trigger = CategoricalParameter(["sell-bb_upper1", "sell-bb_upper2", "sell-bb_upper3", "sell-bb_upper4"], default="sell-bb_upper2", space="sell")

    # Hyperopt Buy Parameters (反向，用作做空时平仓)
    buy_fastema = IntParameter(low=1, high=236, default=7, space='buy', optimize=True, load=True)
    buy_slowema = IntParameter(low=1, high=250, default=6, space='buy', optimize=True, load=True)
    buy_rsi = IntParameter(low=30, high=50, default=37, space='buy', optimize=True, load=True)
    buy_mfi = IntParameter(low=30, high=50, default=31, space='buy', optimize=True, load=True)

    buy_rsi_enabled = CategoricalParameter([True, False], space='buy', optimize=True, default=True)
    buy_mfi_enabled = CategoricalParameter([True, False], space='buy', optimize=True, default=True)
    buy_ema_enabled = CategoricalParameter([True, False], space='buy', optimize=True, default=True)
    buy_trigger = CategoricalParameter(["bb_lower1", "bb_lower2", "bb_lower3", "bb_lower4"], default="bb_lower1", space="buy")

    # leverage_level = IntParameter(1, 5, default=5, space="buy", optimize=False, load=False)
    leverage_level = IntParameter(1, 5, default=3, space="sell", optimize=False, load=False)

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # RSI
        dataframe['rsi'] = ta.RSI(dataframe)
        dataframe['mfi'] = ta.MFI(dataframe)

        # Bollinger Bands 1,2,3 and 4
        bollinger1 = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), window=20, stds=1)
        dataframe['bb_lowerband1'] = bollinger1['lower']
        dataframe['bb_middleband1'] = bollinger1['mid']
        dataframe['bb_upperband1'] = bollinger1['upper']

        bollinger2 = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), window=20, stds=2)
        dataframe['bb_lowerband2'] = bollinger2['lower']
        dataframe['bb_middleband2'] = bollinger2['mid']
        dataframe['bb_upperband2'] = bollinger2['upper']

        bollinger3 = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), window=20, stds=3)
        dataframe['bb_lowerband3'] = bollinger3['lower']
        dataframe['bb_middleband3'] = bollinger3['mid']
        dataframe['bb_upperband3'] = bollinger3['upper']

        bollinger4 = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), window=20, stds=4)
        dataframe['bb_lowerband4'] = bollinger4['lower']
        dataframe['bb_middleband4'] = bollinger4['mid']
        dataframe['bb_upperband4'] = bollinger4['upper']

        dataframe['fastema'] = ta.EMA(dataframe['close'], timeperiod=8)
        dataframe['slowema'] = ta.EMA(dataframe['close'], timeperiod=34)

        # Build EMA rows - combine all ranges to a single set to avoid duplicate calculations.
        # for period in set(
        #         list(self.buy_fastema.range)
        #         + list(self.buy_slowema.range)
        #         + list(self.sell_fastema.range)
        #         + list(self.sell_slowema.range)
        #     ):
        #     dataframe[f'EMA_{period}'] = ta.EMA(dataframe, timeperiod=period)
               # 获取所有可能的EMA周期参数
        # all_periods = set()
        
        # # 添加买入参数范围
        # all_periods.update(range(self.buy_fastema.low, self.buy_fastema.high + 1))
        # all_periods.update(range(self.buy_slowema.low, self.buy_slowema.high + 1))
        
        for period in set(
                list(self.buy_fastema.range)
                + list(self.buy_slowema.range)
                + list(self.sell_fastema.range)
                + list(self.sell_slowema.range)
            ):
            dataframe[f'EMA_{period}'] = ta.EMA(dataframe, timeperiod=period)

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # dataframe['enter_short'] = 0
        # dataframe['enter_tag'] = None
        long_conditions = []
        conditions = []

        if self.sell_rsi_enabled.value:
            conditions.append(dataframe['rsi'] > self.sell_rsi.value)
        if self.sell_mfi_enabled.value:
            conditions.append(dataframe['mfi'] > self.sell_mfi.value)
        if self.sell_ema_enabled.value:
            conditions.append(dataframe[f'EMA_{self.sell_fastema.value}'] < dataframe[f'EMA_{self.sell_slowema.value}'])

        if self.sell_trigger.value == 'sell-bb_upper1':
            conditions.append(dataframe["close"] > dataframe['bb_upperband1'])
        if self.sell_trigger.value == 'sell-bb_upper2':
            conditions.append(dataframe["close"] > dataframe['bb_upperband2'])
        if self.sell_trigger.value == 'sell-bb_upper3':
            conditions.append(dataframe["close"] > dataframe['bb_upperband3'])
        if self.sell_trigger.value == 'sell-bb_upper4':
            conditions.append(dataframe["close"] > dataframe['bb_upperband4'])

        conditions.append(dataframe['volume'] > 0)

        if conditions:
            dataframe.loc[
                reduce(lambda x, y: x & y, conditions),
                'enter_short'] = 1
            return dataframe
            
        # GUARDS
        if self.buy_rsi_enabled.value:
            long_conditions.append(dataframe['rsi'] < self.buy_rsi.value)
        if self.buy_mfi_enabled.value:
            long_conditions.append(dataframe['mfi'] < self.buy_mfi.value)
        if self.buy_ema_enabled.value:
            long_conditions.append(dataframe[f'EMA_{self.buy_fastema.value}'] > dataframe[f'EMA_{self.buy_slowema.value}'])

        # TRIGGERS
        if self.buy_trigger.value == 'bb_lower1':
            long_conditions.append(dataframe["close"] < dataframe['bb_lowerband1'])
        if self.buy_trigger.value == 'bb_lower2':
            long_conditions.append(dataframe["close"] < dataframe['bb_lowerband2'])
        if self.buy_trigger.value == 'bb_lower3':
            long_conditions.append(dataframe["close"] < dataframe['bb_lowerband3'])
        if self.buy_trigger.value == 'bb_lower4':
            long_conditions.append(dataframe["close"] < dataframe['bb_lowerband4'])

        # Check that volume is not 0
        long_conditions.append(dataframe['volume'] > 0)

        if long_conditions:
            dataframe.loc[
                reduce(lambda x, y: x & y, long_conditions),
                'enter_long'] = 1
            return dataframe

        # # GUARDS
        # if self.buy_rsi_enabled.value:
        #     long_conditions.append(dataframe['rsi'] < self.buy_rsi.value)
        # if self.buy_mfi_enabled.value:
        #     long_conditions.append(dataframe['mfi'] < self.buy_mfi.value)
        # if self.buy_ema_enabled.value:
        #     long_conditions.append(dataframe[f'EMA_{self.buy_fastema.value}'] > dataframe[f'EMA_{self.buy_slowema.value}'])

        # # TRIGGERS
        # if self.buy_trigger.value == 'bb_lower1':
        #     long_conditions.append(dataframe["close"] < dataframe['bb_lowerband1'])
        # if self.buy_trigger.value == 'bb_lower2':
        #     long_conditions.append(dataframe["close"] < dataframe['bb_lowerband2'])
        # if self.buy_trigger.value == 'bb_lower3':
        #     long_conditions.append(dataframe["close"] < dataframe['bb_lowerband3'])
        # if self.buy_trigger.value == 'bb_lower4':
        #     long_conditions.append(dataframe["close"] < dataframe['bb_lowerband4'])

        # # Check that volume is not 0
        # long_conditions.append(dataframe['volume'] > 0)

        # if long_conditions:
        #     dataframe.loc[
        #         reduce(lambda x, y: x & y, long_conditions),
        #         'enter_long'] = 1
        #     return dataframe
        
        # 提取当前参数值
        # self.sell_fast_val = int(self.sell_fastema.value)
        # self.sell_slow_val = int(self.sell_slowema.value)

        # # 使用TA-Lib计算EMA（避免列存储）
        # fast_ema = ta.EMA(dataframe['close'], timeperiod=self.sell_fast_val).shift(1)
        # slow_ema = ta.EMA(dataframe['close'], timeperiod=self.sell_slow_val).shift(1)

        # # 生成动态标签
        # dataframe.loc[conditions, 'enter_tag'] = (
        #     "RSI=" + dataframe['rsi'].round(2).astype(str) + "," +
        #     "MFI=" + dataframe['mfi'].round(2).astype(str) + "," +
        #     "FastEMA=" + fast_ema.round(2).astype(str) + "," +
        #     "SlowEMA=" + slow_ema.round(2).astype(str)
        # )
        
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        conditions = []
        long_conditions = []

        if self.buy_rsi_enabled.value:
            conditions.append(dataframe['rsi'] < self.buy_rsi.value)
        if self.buy_mfi_enabled.value:
            conditions.append(dataframe['mfi'] < self.buy_mfi.value)
        if self.buy_ema_enabled.value:
            conditions.append(dataframe[f'EMA_{self.buy_fastema.value}'] > dataframe[f'EMA_{self.buy_slowema.value}'])

        if self.buy_trigger.value == 'bb_lower1':
            conditions.append(dataframe["close"] < dataframe['bb_lowerband1'])
        if self.buy_trigger.value == 'bb_lower2':
            conditions.append(dataframe["close"] < dataframe['bb_lowerband2'])
        if self.buy_trigger.value == 'bb_lower3':
            conditions.append(dataframe["close"] < dataframe['bb_lowerband3'])
        if self.buy_trigger.value == 'bb_lower4':
            conditions.append(dataframe["close"] < dataframe['bb_lowerband4'])

        conditions.append(dataframe['volume'] > 0)

        if conditions:
            dataframe.loc[
                reduce(lambda x, y: x & y, conditions),
                'exit_short'] = 1
            return dataframe
    
        # GUARDS
        if self.sell_rsi_enabled.value:
            long_conditions.append(dataframe['rsi'] > self.sell_rsi.value)
        if self.sell_mfi_enabled.value:
            long_conditions.append(dataframe['mfi'] > self.sell_mfi.value)
        if self.sell_ema_enabled.value:
            long_conditions.append(dataframe[f'EMA_{self.sell_fastema.value}'] < dataframe[f'EMA_{self.sell_slowema.value}'])

        # TRIGGERS
        if self.sell_trigger.value == 'sell-bb_upper1':
            long_conditions.append(dataframe["close"] > dataframe['bb_upperband1'])
        if self.sell_trigger.value == 'sell-bb_upper2':
            long_conditions.append(dataframe["close"] > dataframe['bb_upperband2'])
        if self.sell_trigger.value == 'sell-bb_upper3':
            long_conditions.append(dataframe["close"] > dataframe['bb_upperband3'])
        if self.sell_trigger.value == 'sell-bb_upper4':
            long_conditions.append(dataframe["close"] > dataframe['bb_upperband4'])

        # Check that volume is not 0
        long_conditions.append(dataframe['volume'] > 0)

        if long_conditions:
            dataframe.loc[
                reduce(lambda x, y: x & y, long_conditions),
                'exit_long'] = 1
            return dataframe
         # GUARDS
        # if self.sell_rsi_enabled.value:
        #     long_conditions.append(dataframe['rsi'] > self.sell_rsi.value)
        # if self.sell_mfi_enabled.value:
        #     long_conditions.append(dataframe['mfi'] > self.sell_mfi.value)
        # if self.sell_ema_enabled.value:
        #     long_conditions.append(dataframe[f'EMA_{self.sell_fastema.value}'] < dataframe[f'EMA_{self.sell_slowema.value}'])

        # # TRIGGERS
        # if self.sell_trigger.value == 'sell-bb_upper1':
        #     long_conditions.append(dataframe["close"] > dataframe['bb_upperband1'])
        # if self.sell_trigger.value == 'sell-bb_upper2':
        #     long_conditions.append(dataframe["close"] > dataframe['bb_upperband2'])
        # if self.sell_trigger.value == 'sell-bb_upper3':
        #     long_conditions.append(dataframe["close"] > dataframe['bb_upperband3'])
        # if self.sell_trigger.value == 'sell-bb_upper4':
        #     long_conditions.append(dataframe["close"] > dataframe['bb_upperband4'])

        # # Check that volume is not 0
        # long_conditions.append(dataframe['volume'] > 0)

        # if long_conditions:
        #     dataframe.loc[
        #         reduce(lambda x, y: x & y, long_conditions),
        #         'exit_long'] = 1
            # return dataframe

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                proposed_leverage: float, max_leverage: float, side: str,
                **kwargs) -> float:
        return self.leverage_level.value


    
import ccxt

# 初始化 Binance 交易所，并指定使用合约市场
exchange = ccxt.binance(
    {
        "options": {
            "defaultType": "future",  # 使用币安永续合约（USDT 计价）
        },
        "enableRateLimit": True,
    }
)

# 加载市场
markets = exchange.load_markets()

# 过滤出 USDT 永续合约交易对
usdt_futures = [
    symbol
    for symbol, market in markets.items()
    if market.get("contract") and market["quote"] == "USDT"
]

# 获取 ticker 信息
tickers = exchange.fetch_tickers(usdt_futures)

# 提取并按 quoteVolume 排序
volume_data = []
for symbol, ticker in tickers.items():
    quote_volume = ticker.get("quoteVolume", 0)
    volume_data.append((symbol, quote_volume))

# 排序并取前15
top_15 = sorted(volume_data, key=lambda x: x[1], reverse=True)[:15]

# 输出结果
print("📊 币安 USDT 永续合约交易量 Top 15（24小时）:")
for i, (symbol, volume) in enumerate(top_15, 1):
    print(f"{i:>2}. {symbol:12} | 成交量(USDT): {volume:,.2f}")

